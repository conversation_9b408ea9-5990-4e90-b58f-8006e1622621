{"LOG": true, "CLAUDE_PATH": "/Users/<USER>/.claude/local/claude", "HOST": "127.0.0.1", "PORT": 8080, "APIKEY": "1", "API_TIMEOUT_MS": 600000, "transformers": [{"path": "/Users/<USER>/.claude-code-router/plugins/gemini-cli.js", "options": {"project": "x"}}], "Providers": [{"name": "siliconflow", "api_base_url": "https://api.moonshot.cn/v1/chat/completions", "api_key": "sk-", "models": ["kimi-k2-0711-preview"], "transformer": {"use": [["maxtoken", {"max_tokens": 130000}]]}}, {"name": "kimi", "api_base_url": "https://api.moonshot.cn/v1/chat/completions", "api_key": "sk-", "models": ["kimi-k2-0711-preview"]}, {"name": "groq", "api_base_url": "https://api.groq.com/openai/v1/chat/completions", "api_key": "", "models": ["moonshotai/kimi-k2-instruct"], "transformer": {"use": [["maxtoken", {"max_tokens": 16384}], "groq"]}}, {"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "sk-or-v1-", "models": ["google/gemini-2.5-pro-preview", "anthropic/claude-sonnet-4", "anthropic/claude-3.5-sonnet", "anthropic/claude-3.7-sonnet:thinking", "deepseek/deepseek-chat-v3-0324", "@preset/kimi"], "transformer": {"use": ["openrouter"], "deepseek/deepseek-chat-v3-0324": {"use": ["tooluse"]}}}, {"name": "deepseek", "api_base_url": "https://api.deepseek.com/chat/completions", "api_key": "sk-", "models": ["deepseek-chat", "deepseek-reasoner"], "transformer": {"use": ["deepseek"], "deepseek-chat": {"use": ["tooluse"]}}}, {"name": "test", "api_base_url": "https://tbai.xin/v1/chat/completions", "api_key": "sk-", "models": ["gemini-2.5-pro"]}, {"name": "ollama", "api_base_url": "http://localhost:11434/v1/chat/completions", "api_key": "ollama", "models": ["qwen2.5-coder:latest"]}, {"name": "gemini", "api_base_url": "https://generativelanguage.googleapis.com/v1beta/models/", "api_key": "", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "volcengine", "api_base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "api_key": "sk-xxx", "models": ["deepseek-v3-250324", "deepseek-r1-250528"], "transformer": {"use": ["deepseek"]}}, {"name": "gemini-cli", "api_base_url": "https://cloudcode-pa.googleapis.com/v1internal", "api_key": "sk-xxx", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini-cli"]}}, {"name": "azure", "api_base_url": "https://your-resource-name.openai.azure.com/", "api_key": "", "models": ["gpt-4"]}], "Router": {"default": "gemini-cli,gemini-2.5-pro", "background": "gemini-cli,gemini-2.5-flash", "think": "gemini-cli,gemini-2.5-pro", "longContext": "gemini-cli,gemini-2.5-pro", "webSearch": "gemini-cli,gemini-2.5-flash"}}