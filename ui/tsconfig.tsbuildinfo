{"root": ["./src/app.tsx", "./src/i18n.ts", "./src/main.tsx", "./src/routes.tsx", "./src/vite-env.d.ts", "./src/components/configprovider.tsx", "./src/components/jsoneditor.tsx", "./src/components/login.tsx", "./src/components/protectedroute.tsx", "./src/components/providerlist.tsx", "./src/components/providers.tsx", "./src/components/publicroute.tsx", "./src/components/router.tsx", "./src/components/settingsdialog.tsx", "./src/components/transformerlist.tsx", "./src/components/transformers.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/combo-input.tsx", "./src/components/ui/combobox.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/multi-combobox.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/toast.tsx", "./src/lib/api.ts", "./src/lib/utils.ts"], "version": "5.8.3"}